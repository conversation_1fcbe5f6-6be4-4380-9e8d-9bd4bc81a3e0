package model

import (
	"github.com/lib/pq"
	"gorm.io/gorm"
)

// 库存表，用于管理库存信息
type WmsStock struct {
	gorm.Model
	SkuId     uint          `gorm:"default:0; index; comment:物料规格ID"`
	AreaPath  pq.Int64Array `gorm:"type:integer[]; index; comment:库位路径"`
	Num       float64       `gorm:"type:numeric(10,2); default:0; index; comment:库存数量"`
	TenantId  uint          `gorm:"default:0; index; comment:租户ID" copier:"-"`
	CreatedBy string        `gorm:"size:64; comment:创建人" copier:"-"`
	UpdatedBy string        `gorm:"size:64; comment:操作人" copier:"-"`
}

func (m *WmsStock) TableName() string {
	return "wms_stock"
}
