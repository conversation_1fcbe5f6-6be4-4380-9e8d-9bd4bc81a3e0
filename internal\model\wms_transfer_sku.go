package model

import (
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// 调拨单物料明细表，用于管理仓库的调拨单物料明细信息
type WmsTransferSku struct {
	gorm.Model
	TransferId  uint           `gorm:"default:0; index; comment:调拨单ID"`
	SkuId       uint           `gorm:"default:0; index; comment:物料规格ID"`
	Num         float64        `gorm:"type:numeric(10,2); default:0; comment:调拨数量"`
	FromAreaNum datatypes.JSON `gorm:"type:jsonb; comment:来源库配置"`
	ToAreaNum   datatypes.JSON `gorm:"type:jsonb; comment:目标库配置"`
	Status      bool           `gorm:"default:false; index; comment:状态"`
	TenantId    uint           `gorm:"default:0; index; comment:租户ID" copier:"-"`
	CreatedBy   string         `gorm:"size:64; comment:创建人" copier:"-"`
	UpdatedBy   string         `gorm:"size:64; comment:操作人" copier:"-"`
}

func (m *WmsTransferSku) TableName() string {
	return "wms_transfer_sku"
}
